import pandas as pd
import os

def load_trades_data():
    """
    Loads trade data from Excel, falling back to CSV if necessary.
    """
    data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data', 'trade_analysis')
    excel_path = os.path.join(data_dir, 'trades.xlsx')
    csv_path = os.path.join(data_dir, 'trades.csv')

    try:
        df = pd.read_excel(excel_path)
    except (FileNotFoundError, PermissionError):
        try:
            df = pd.read_csv(csv_path)
        except FileNotFoundError:
            return None
    return df

def get_progressive_exposure_recommendation(
    modest_dd_multiplier=1.5,
    significant_dd_multiplier=3.0,
    volatility_window=50, # Rolling window for volatility
    momentum_window_days=30 # Time-based window for momentum
):
    """
    Analyzes performance using a hybrid "Adaptive Volatility & Time-Based Momentum" approach.
    Returns 'Full', 'Half', or 'Quarter'.
    """
    df = load_trades_data()
    if df is None or df.empty or 'R-Multiple' not in df.columns:
        print("Trade data with R-Multiples not available. Defaulting to Half size.")
        return "Half"

    # --- 1. Calculate Equity Curve in R and its Adaptive Volatility ---
    df['SellDate'] = pd.to_datetime(df['SellDate'])
    df.sort_values(by='SellDate', inplace=True)
    df['Cumulative R'] = df['R-Multiple'].cumsum()

    # Use a rolling window for a more adaptive volatility measure
    r_volatility = df['R-Multiple'].rolling(window=volatility_window, min_periods=1).std().iloc[-1]

    # --- 2. Define Drawdown Thresholds in R ---
    modest_drawdown_threshold_r = modest_dd_multiplier * r_volatility
    significant_drawdown_threshold_r = significant_dd_multiplier * r_volatility

    # --- 3. Calculate Current Drawdown in R ---
    peak_r = df['Cumulative R'].max()
    current_r = df['Cumulative R'].iloc[-1]
    current_drawdown_r = peak_r - current_r

    # --- 4. Calculate Time-Based Recent Momentum in R ---
    thirty_days_ago = pd.Timestamp.now() - pd.Timedelta(days=momentum_window_days)
    recent_trades = df[df['SellDate'] >= thirty_days_ago]

    use_momentum = len(recent_trades) >= 3 # Minimum trade guardrail
    recent_r_avg = recent_trades['R-Multiple'].mean() if use_momentum else 0

    # --- 5. Combine Factors for Final Recommendation ---
    recommendation = "Half" # Default

    if current_drawdown_r > significant_drawdown_threshold_r:
        recommendation = "Quarter"
        reason = f"Significant R-drawdown ({current_drawdown_r:.2f}R > {significant_drawdown_threshold_r:.2f}R)"
    elif current_drawdown_r > modest_drawdown_threshold_r:
        if use_momentum and recent_r_avg > 0:
            recommendation = "Half"
            reason = f"Modest R-drawdown, but recent momentum is positive ({recent_r_avg:.2f}R avg)"
        else:
            recommendation = "Quarter"
            reason = f"Modest R-drawdown and recent momentum is negative or insignificant ({recent_r_avg:.2f}R avg)"
    else: # Near peak equity in R
        if use_momentum and recent_r_avg > 0:
            recommendation = "Full"
            reason = f"Near peak R-equity and recent momentum is positive ({recent_r_avg:.2f}R avg)"
        else:
            recommendation = "Half"
            reason = f"Near peak R-equity, but recent momentum is negative or insignificant ({recent_r_avg:.2f}R avg)"

    # --- 6. Clear and Informative Logging ---
    print("\n--- Adaptive Volatility & Time-Based Momentum Sizing ---")
    print(f"Adaptive R-Volatility ({volatility_window}-trade rolling): {r_volatility:.2f}R")
    print(f"Current R-Drawdown: {current_drawdown_r:.2f}R")
    print(f"Recent {momentum_window_days}-Day Avg R-Multiple: {recent_r_avg:.2f}R")
    print(f"Reasoning: {reason}")
    print("*****************************************************")

    return recommendation

if __name__ == '__main__':
    # Example usage
    recommendation = get_progressive_exposure_recommendation()
    print(f"\nRecommended Position Size: {recommendation}")
